import React, { useState } from "react";
import SellerLayout from "../../components/seller/SellerLayout";
import "../../styles/SellerDashboard.css";
import { FaFileAlt } from "react-icons/fa";
import { IoEyeSharp } from "react-icons/io5";
import { BsThreeDotsVertical } from "react-icons/bs";
import { LiaComment } from "react-icons/lia";

const stats = [
  { count: "08", label: "Total Strategies", color: "purple" },
  { count: "02", label: "Requests", color: "orange" },
  { count: "03", label: "Bids", color: "green" },
];

const initialStrategies = [
  {
    id: 1,
    title: "<PERSON> and Coaching Philosophies...",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: true,
  },
  {
    id: 2,
    title: "<PERSON> - Early Transition Offensive Concepts",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: false,
  },
];

const requests = [
  {
    id: "#2345678",
    title: initialStrategies[0].title,
    date: initialStrategies[0].date,
    price: initialStrategies[0].price,
    amount: "$19.00",
    user: "<PERSON>",
  },
  {
    id: "#2345679",
    title: initialStrategies[1].title,
    date: initialStrategies[1].date,
    price: initialStrategies[1].price,
    amount: "$18.00",
    user: "Olivia Smart",
  },
];

const bids = [...requests];

const StatsCard = ({ count, label, color }) => (
  <div className={`stats-card ${color}`}>
    <h2>{count}</h2>
    <p>{label}</p>
  </div>
);

const StrategyRow = ({ item, toggle }) => (
  <tr>
    <td>{item.id}</td>
    <td>
      <div className="video-title">
        <FaFileAlt className="video-icon" />
        <span>{item.title}</span>
      </div>
    </td>
    <td>{item.date}</td>
    <td>{item.price}</td>
    <td>
      <label className="switch">
        <input
          type="checkbox"
          checked={item.status}
          onChange={() => toggle(item.id)}
        />
        <span className="slider round"></span>
      </label>
    </td>
    <td>
      <div className="action-icons">
        <IoEyeSharp className="action-icon" />
      </div>
    </td>
  </tr>
);

const RequestRow = ({ item, showUser = true }) => (
  <tr>
    <td>{item.id}</td>
    <td>
      <div className="video-title">
        <FaFileAlt className="video-icon" />
        <span>{item.title}</span>
      </div>
    </td>
    <td>{item.date}</td>
    <td>{item.price}</td>
    <td>{item.amount}</td>
    {showUser ? <td>{item.user}</td> : null}
    <td>
      <div className="action-icons">
        <IoEyeSharp className="action-icon" />
        <LiaComment className="action-icon" />
      </div>
    </td>
  </tr>
);

const BidRow = ({ item }) => (
  <tr>
    <td>{item.id}</td>
    <td>
      <div className="video-title">
        <FaFileAlt className="video-icon" />
        <span>{item.title}</span>
      </div>
    </td>
    <td>{item.date}</td>
    <td>{item.price}</td>
    <td>{item.amount}</td>
    <td>
      <div className="action-icons">
        <BsThreeDotsVertical className="action-icon" />
      </div>
    </td>
  </tr>
);

const SellerDashboard = () => {
  const [strategies, setStrategies] = useState(initialStrategies);

  const toggleStatus = (id) => {
    setStrategies((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, status: !item.status } : item
      )
    );
  };

  return (
    <SellerLayout>
      <div className="dashboard-container">
        <div className="stats-container">
          {stats.map((stat, idx) => (
            <StatsCard key={idx} {...stat} />
          ))}
        </div>

        <div className="section">
          <div className="section-header">
            <h3>My Sports Strategies</h3>
            <a href="/downloads">View All Downloads</a>
          </div>
          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th>No.</th>
                  <th>Videos/Documents</th>
                  <th>Date</th>
                  <th>Price</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {strategies.map((item) => (
                  <StrategyRow key={item.id} item={item} toggle={toggleStatus} />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="section">
          <div className="section-header">
            <h3>New Requests</h3>
            <a href="/requests">View All Requests</a>
          </div>
          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th>Order Id</th>
                  <th>Video/Documents</th>
                  <th>Date</th>
                  <th>Price</th>
                  <th>Requested Amount</th>
                  <th>Requested Customer</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {requests.map((item) => (
                  <RequestRow key={item.id} item={item} showUser={true} />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="section">
          <div className="section-header">
            <h3>New Bids</h3>
            <a href="/bids">View All Bids</a>
          </div>
          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th>Bid Id</th>
                  <th>Video/Documents</th>
                  <th>Date</th>
                  <th>Price</th>
                  <th>Bid Amount</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {bids.map((item) => (
                  <BidRow key={item.id} item={item} />
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default SellerDashboard;
